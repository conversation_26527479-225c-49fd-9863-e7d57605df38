import { useCallback, useEffect, useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { TokenManager, TeamManager } from '@/libs/storage'
import { UploadTask } from '@app/shared/types/database.types'

/**
 * 上传任务状态
 */
interface TaskState {
  isLoading: boolean
  error: string | null
  isOperating: boolean
}

/**
 * 上传任务管理 Hook
 * 提供简洁的上传任务管理接口
 */
export function useTask() {
  const queryClient = useQueryClient()
  const currentUser = TokenManager.getUserId()
  const currentTeam = TeamManager.current()
  
  // 内部状态管理
  const [state, setState] = useState<TaskState>({
    isLoading: false,
    error: null,
    isOperating: false
  })

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<TaskState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }, [])

  // 获取所有任务
  const { 
    data: tasks = [], 
    isLoading: isQueryLoading, 
    refetch: refetchTasks,
    error: queryError 
  } = useQuery({
    queryKey: ['uploadTasks', currentUser, currentTeam],
    queryFn: async () => {
      if (!currentUser) return []
      return window.uploadTask.getUserTasks({
        uid: String(currentUser),
        teamId: currentTeam
      })
    },
    enabled: !!currentUser,
    refetchInterval: 5000 // 每5秒刷新一次
  })

  // 获取任务统计
  const { data: stats } = useQuery({
    queryKey: ['uploadTaskStats', currentUser, currentTeam],
    queryFn: async () => {
      if (!currentUser) return null
      return window.uploadTask.getTaskStats({
        uid: String(currentUser),
        teamId: currentTeam
      })
    },
    enabled: !!currentUser,
    refetchInterval: 10000 // 每10秒刷新一次
  })

  // 获取队列状态
  const { data: queueStatus } = useQuery({
    queryKey: ['uploadQueueStatus'],
    queryFn: () => window.uploadTask.getQueueStatus(),
    refetchInterval: 3000 // 每3秒刷新一次
  })

  // 获取队列配置
  const { data: queueConfig } = useQuery({
    queryKey: ['uploadQueueConfig'],
    queryFn: () => window.uploadTask.getQueueConfig(),
    staleTime: 60000 // 配置变化不频繁，1分钟内不重新获取
  })

  // 创建任务
  const createTaskMutation = useMutation({
    mutationFn: async (params: UploadTask.CreateParams) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.create(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 更新任务
  const updateTaskMutation = useMutation({
    mutationFn: async ({ id, params }: { id: number, params: UploadTask.UpdateParams }) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.update({ id, params })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 删除任务
  const deleteTaskMutation = useMutation({
    mutationFn: async (id: number) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.delete(id)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 开始上传
  const startUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.startUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 暂停上传
  const pauseUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.pauseUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 恢复上传
  const resumeUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.resumeUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 取消上传
  const cancelUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.cancelUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 重试上传
  const retryUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.retryUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 批量操作
  const batchOperationMutation = useMutation({
    mutationFn: async (params: UploadTask.BatchParams) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.batchOperation(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 清理已完成任务
  const cleanupCompletedMutation = useMutation({
    mutationFn: async (olderThanDays?: number) => {
      if (!currentUser) return 0
      updateState({ isOperating: true, error: null })
      return window.uploadTask.cleanupCompleted({
        uid: String(currentUser),
        teamId: currentTeam,
        olderThanDays
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 更新队列配置
  const updateQueueConfigMutation = useMutation({
    mutationFn: async (config: Partial<UploadTask.QueueConfig>) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.updateQueueConfig(config)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadQueueConfig'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 选择文件
  const selectFilesMutation = useMutation({
    mutationFn: async (options?: {
      multiple?: boolean
      filters?: Array<{ name: string, extensions: string[] }>
    }) => {
      return window.uploadTask.selectFiles(options || {})
    }
  })

  // 上传文件内容
  const uploadFileContentMutation = useMutation({
    mutationFn: async (params: {
      taskId: number
      fileContent: ArrayBuffer | Uint8Array
    }) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.uploadFileContent(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 从路径上传文件
  const uploadFromPathMutation = useMutation({
    mutationFn: async (params: {
      taskId: number
      filePath: string
    }) => {
      updateState({ isOperating: true, error: null })
      return window.uploadTask.uploadFromPath(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      updateState({ isOperating: false })
    },
    onError: (error: Error) => {
      updateState({ isOperating: false, error: error.message })
    }
  })

  // 监听上传进度事件
  useEffect(() => {
    const handleProgressEvent = (_event: any, progressEvents: UploadTask.ProgressEvent[]) => {
      // 批量更新任务进度
      queryClient.setQueryData(['uploadTasks', currentUser, currentTeam], (oldTasks: UploadTask.IUploadTask[] | undefined) => {
        if (!oldTasks) return oldTasks

        const progressMap = new Map<number, number>()
        progressEvents.forEach(event => {
          progressMap.set(event.task_id, event.progress)
        })

        return oldTasks.map(task => {
          const newProgress = progressMap.get(task.id)
          if (newProgress !== undefined) {
            const shouldUpdateStatus = task.status === UploadTask.Status.PENDING && newProgress > 0
            return {
              ...task,
              progress: newProgress,
              status: shouldUpdateStatus ? UploadTask.Status.UPLOADING : task.status,
              updated_at: Date.now()
            }
          }
          return task
        })
      })
    }

    // 监听批量进度事件
    window.electronAPI?.ipcRenderer.on('batch-upload-progress', handleProgressEvent)

    return () => {
      window.electronAPI?.ipcRenderer.removeListener('batch-upload-progress', handleProgressEvent)
    }
  }, [currentUser, currentTeam, queryClient])

  // 同步加载状态
  useEffect(() => {
    updateState({
      isLoading: isQueryLoading,
      error: queryError?.message || null
    })
  }, [isQueryLoading, queryError, updateState])

  // 便捷查询方法
  const getTasksByStatus = useCallback((status: UploadTask.Status) => {
    return tasks.filter(task => task.status === status)
  }, [tasks])

  const getTasksByFolder = useCallback(async (folderId: string) => {
    if (!currentUser) return []
    return window.uploadTask.getTasksByFolder({
      folderId,
      uid: String(currentUser),
      teamId: currentTeam
    })
  }, [currentUser, currentTeam])

  const searchTasks = useCallback(async (keyword: string) => {
    if (!currentUser) return []
    return window.uploadTask.searchTasks({
      keyword,
      uid: String(currentUser),
      teamId: currentTeam
    })
  }, [currentUser, currentTeam])

  // 便捷状态查询
  const getPendingTasks = useCallback(() => getTasksByStatus(UploadTask.Status.PENDING), [getTasksByStatus])
  const getUploadingTasks = useCallback(() => getTasksByStatus(UploadTask.Status.UPLOADING), [getTasksByStatus])
  const getPausedTasks = useCallback(() => getTasksByStatus(UploadTask.Status.PAUSED), [getTasksByStatus])
  const getCompletedTasks = useCallback(() => getTasksByStatus(UploadTask.Status.COMPLETED), [getTasksByStatus])
  const getFailedTasks = useCallback(() => getTasksByStatus(UploadTask.Status.FAILED), [getTasksByStatus])
  const getCancelledTasks = useCallback(() => getTasksByStatus(UploadTask.Status.CANCELLED), [getTasksByStatus])

  // 获取单个任务
  const getTaskById = useCallback((id: number) => {
    return tasks.find(task => task.id === id)
  }, [tasks])

  // 清除错误状态
  const clearError = useCallback(() => {
    updateState({ error: null })
  }, [updateState])

  // 手动刷新数据
  const refresh = useCallback(() => {
    refetchTasks()
    queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
  }, [refetchTasks, queryClient])

  return {
    // 数据状态
    tasks,
    stats,
    queueStatus,
    queueConfig,
    isLoading: state.isLoading,
    isOperating: state.isOperating,
    error: state.error,

    // 查询方法
    getTasksByStatus,
    getTasksByFolder,
    searchTasks,
    getPendingTasks,
    getUploadingTasks,
    getPausedTasks,
    getCompletedTasks,
    getFailedTasks,
    getCancelledTasks,
    getTaskById,

    // CRUD 操作
    createTask: createTaskMutation.mutateAsync,
    updateTask: updateTaskMutation.mutateAsync,
    deleteTask: deleteTaskMutation.mutateAsync,

    // 上传控制操作
    startUpload: startUploadMutation.mutateAsync,
    pauseUpload: pauseUploadMutation.mutateAsync,
    resumeUpload: resumeUploadMutation.mutateAsync,
    cancelUpload: cancelUploadMutation.mutateAsync,
    retryUpload: retryUploadMutation.mutateAsync,

    // 批量操作
    batchOperation: batchOperationMutation.mutateAsync,

    // 文件操作
    selectFiles: selectFilesMutation.mutateAsync,
    uploadFileContent: uploadFileContentMutation.mutateAsync,
    uploadFromPath: uploadFromPathMutation.mutateAsync,

    // 管理操作
    cleanupCompleted: cleanupCompletedMutation.mutateAsync,
    updateQueueConfig: updateQueueConfigMutation.mutateAsync,

    // 工具方法
    refresh,
    clearError,

    // 操作状态
    isCreating: createTaskMutation.isPending,
    isUpdating: updateTaskMutation.isPending,
    isDeleting: deleteTaskMutation.isPending,
    isStarting: startUploadMutation.isPending,
    isPausing: pauseUploadMutation.isPending,
    isResuming: resumeUploadMutation.isPending,
    isCancelling: cancelUploadMutation.isPending,
    isRetrying: retryUploadMutation.isPending,
    isBatchOperating: batchOperationMutation.isPending,
    isSelectingFiles: selectFilesMutation.isPending,
    isUploadingContent: uploadFileContentMutation.isPending,
    isUploadingFromPath: uploadFromPathMutation.isPending,
    isCleaning: cleanupCompletedMutation.isPending,
    isUpdatingConfig: updateQueueConfigMutation.isPending
  }
}
