import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Grid3X3, List, MoreHorizontal, Search } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { MixcutProvider, useMixcutContext, useVirtualTab } from '@/contexts'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore'
import { BatchUploadOverlay } from '@/components/BatchUploadOverlay'
import { useQuery } from '@tanstack/react-query'
import { Player, PlayerRef } from '@remotion/player'
import { Renderer } from '@clipnest/overlay-renderer'
import { loadProjectState } from '@/modules/video-editor/shared'
import { OverlayType, VideoOverlay } from '@clipnest/remotion-shared/types'

import { SavedMixcutDeleteButton, SavedMixcutExportButton, SavedMixcutListPanel } from './saved'
import { GeneratedMixcutListPanel, MixcutRuleButton, VideoPreviewFrame } from './generated' // 顶部 - 标签栏组件

// 顶部 - 标签栏组件
const PreviewTabBar = () => {
  const { activeTab, setActiveTab } = useMixcutContext()

  return (
    <div className="border-b border-border bg-background">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="h-12 w-full justify-start rounded-none border-0 bg-transparent p-0">
          <TabsTrigger
            value="generation"
            className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none"
          >
            生成混剪
          </TabsTrigger>
          <TabsTrigger
            value="saved"
            className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none"
          >
            我保存的混剪
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  )
}

// 工具栏组件
const Toolbar = () => {
  const [viewMode, setViewMode] = useState('grid')
  const {
    activeTab,
    generation: { selectedIndices, generateCombinations, uploadSelectedPreviews },
  } = useMixcutContext()

  return (
    <div className="flex items-center justify-between p-4 border-b border-border bg-background">
      <div className="flex items-center space-x-4">
        {/* 视图切换按钮 */}
        <div className="flex items-center space-x-1">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('grid')}
            className="h-8 w-8 p-0"
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="h-8 w-8 p-0"
          >
            <List className="h-4 w-4" />
          </Button>
        </div>

        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="搜索视频..."
            className="pl-9 w-64"
          />
        </div>

        <Select defaultValue="invisible">
          <SelectTrigger className="w-40">
            <SelectValue placeholder="" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="invisible">当前脚本内去重</SelectItem>
            <SelectItem value="visible">当前项目内去重</SelectItem>
          </SelectContent>
        </Select>

        <Select defaultValue="basic">
          <SelectTrigger className="w-40">
            <SelectValue placeholder="" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="basic">严谨去重算法</SelectItem>
            <SelectItem value="advanced">标准去重算法</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center space-x-2">
        {/* 根据 activeTab 动态显示不同的按钮 */}
        {activeTab === 'generation' ? (
          <>
            <MixcutRuleButton />

            <Button variant="default" size="sm" onClick={generateCombinations}>
              生成混剪预览
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={uploadSelectedPreviews}
              disabled={selectedIndices.size === 0}
            >
              保存所选混剪 ({selectedIndices.size})
            </Button>
          </>
        ) : (
          <>
            <SavedMixcutDeleteButton />
            <SavedMixcutExportButton />
          </>
        )}
      </div>
    </div>
  )
}

// 中右 - 混剪结果分镜预览
const StoryboardPanel: React.FC = () => {
  const { playerOverlays, generation: { activeItem } } = useMixcutContext()

  const items = useMemo(() => {
    return playerOverlays
      .filter(o => o.type === OverlayType.VIDEO && typeof o.storyboardIndex === 'number')
      .sort((a, b) => a.storyboardIndex! - b.storyboardIndex!)
  }, [playerOverlays])

  if (!items.length || !activeItem) return null

  return (
    <div className="border-l border-border px-4 pt-8">
      <div className="pb-3">分镜视频预览</div>
      <div className="flex flex-col gap-3 overflow-y-auto">
        {items.map(item => (
          <div key={item.id} >
            <div className="w-32 h-48 bg-gray-500 rounded-sm">
              <VideoPreviewFrame overlay={item as VideoOverlay} />
            </div>
            <div className="text-sm text-gray-400 mt-1.5">
              分镜{item.storyboardIndex! + 1} - 轨道{activeItem.combination[item.storyboardIndex!]}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// 右侧 - 视频预览面板组件
const VideoPreviewPanel = () => {
  const { playerOverlays, state: { playerMetadata } } = useMixcutContext()

  const playerRef = useRef<PlayerRef | null>(null)

  const { width, height, fps, durationInFrames } = playerMetadata

  useEffect(() => {
    const player = playerRef.current

    if (player) {
      player.seekTo(0)
      player.play()
    }
  }, [playerOverlays])

  return (
    <div className="w-[30vw] border-l border-border bg-background flex flex-col">
      {/* 预览标题 */}
      <div className="p-4 border-b border-border">
        <h3 className="text-sm font-medium text-foreground flex items-center">
          预览窗口
          <Button variant="ghost" size="sm" className="ml-auto h-6 w-6 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </h3>
      </div>

      {/* 视频播放器区域 */}
      <div className="flex-1 bg-black relative px-4">
        {
          playerOverlays.length
            ? (
              <Player
                ref={playerRef}
                loop={false}
                component={Renderer}
                compositionWidth={width}
                compositionHeight={height}
                style={{
                  width: '100%',
                  height: '100%',
                }}
                fps={fps}
                durationInFrames={durationInFrames}
                inputProps={{
                  overlays: playerOverlays,
                  playerMetadata
                } as any}
              />
            )
            : (
              // 视频播放器占位
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-white/60 text-sm text-center">在左侧选择一个结果后 <br />可以在此处预览完整视频</div>
              </div>
            )
        }
      </div>
    </div>
  )
}

// 主内容区域组件
const PreviewMainContent = () => {
  const { activeTab } = useMixcutContext()

  return (
    <div className="flex flex-1 overflow-hidden">
      {activeTab === 'generation' ? <GeneratedMixcutListPanel /> : <SavedMixcutListPanel />}
      <StoryboardPanel />
      <VideoPreviewPanel />
    </div>
  )
}

export default function MixcutPage() {
  const { params, id } = useVirtualTab('Mixcut')
  const scriptId = params.id
  const { closeTab } = useVirtualTabsStore()

  const { data: state = null, isLoading } = useQuery({
    queryKey: ['PROJECT_STATE', scriptId],
    queryFn: () => loadProjectState(scriptId, true),
  })

  if (!scriptId || !state) {
    if (isLoading) return <>Loading...</>

    closeTab(id)
    return null
  }

  return (
    <MixcutProvider state={state} defaultTab={params.defaultTab}>
      <div className="flex flex-col h-screen bg-background">
        <PreviewTabBar />
        <Toolbar />
        <PreviewMainContent />
      </div>

      {/* 批量上传进度遮罩 */}
      <BatchUploadOverlay />
    </MixcutProvider>
  )
}
