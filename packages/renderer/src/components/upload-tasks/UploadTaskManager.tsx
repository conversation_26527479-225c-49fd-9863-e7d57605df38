import React from 'react'
import { useUploadTasks } from '@/hooks/useUploadTasks'
import { UploadTaskStatus } from '@app/shared/types/upload-task.types'
import { Button } from '@/components/ui/button'

import { Badge } from '@/components/ui/badge'
import { 
  Play, 
  Pause, 
  Square, 
  RotateCcw, 
  Trash2, 
  Upload,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle
} from 'lucide-react'

/**
 * 上传任务管理器组件
 */
export const UploadTaskManager: React.FC = () => {
  const {
    tasks,
    stats,
    queueStatus,
    isLoading,
    startUpload,
    pauseUpload,
    resumeUpload,
    cancelUpload,
    retryUpload,
    deleteTask,
    cleanupCompleted,
    isOperating
  } = useUploadTasks()

  // 获取状态图标
  const getStatusIcon = (status: UploadTaskStatus) => {
    switch (status) {
      case UploadTaskStatus.PENDING:
        return <Clock className="w-4 h-4 text-gray-500" />
      case UploadTaskStatus.UPLOADING:
        return <Upload className="w-4 h-4 text-blue-500 animate-pulse" />
      case UploadTaskStatus.PAUSED:
        return <Pause className="w-4 h-4 text-yellow-500" />
      case UploadTaskStatus.COMPLETED:
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case UploadTaskStatus.FAILED:
        return <XCircle className="w-4 h-4 text-red-500" />
      case UploadTaskStatus.CANCELLED:
        return <Square className="w-4 h-4 text-gray-500" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />
    }
  }

  // 获取状态文本
  const getStatusText = (status: UploadTaskStatus) => {
    const statusMap = {
      [UploadTaskStatus.PENDING]: '等待上传',
      [UploadTaskStatus.UPLOADING]: '上传中',
      [UploadTaskStatus.PAUSED]: '已暂停',
      [UploadTaskStatus.COMPLETED]: '上传完成',
      [UploadTaskStatus.FAILED]: '上传失败',
      [UploadTaskStatus.CANCELLED]: '已取消'
    }
    return statusMap[status] || '未知状态'
  }

  // 获取状态颜色
  const getStatusColor = (status: UploadTaskStatus) => {
    switch (status) {
      case UploadTaskStatus.PENDING:
        return 'secondary'
      case UploadTaskStatus.UPLOADING:
        return 'default'
      case UploadTaskStatus.PAUSED:
        return 'outline'
      case UploadTaskStatus.COMPLETED:
        return 'default'
      case UploadTaskStatus.FAILED:
        return 'destructive'
      case UploadTaskStatus.CANCELLED:
        return 'secondary'
      default:
        return 'secondary'
    }
  }



  // 处理任务操作
  const handleTaskAction = async (taskId: number, action: string) => {
    try {
      switch (action) {
        case 'start':
          await startUpload(taskId)
          break
        case 'pause':
          await pauseUpload(taskId)
          break
        case 'resume':
          await resumeUpload(taskId)
          break
        case 'cancel':
          await cancelUpload(taskId)
          break
        case 'retry':
          await retryUpload(taskId)
          break
        case 'delete':
          await deleteTask(taskId)
          break
      }
    } catch (error) {
      console.error('任务操作失败:', error)
    }
  }

  if (isLoading) {
    return <div className="p-4">加载中...</div>
  }

  return (
    <div className="p-4 space-y-4">
      {/* 统计信息 */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="text-sm text-blue-600">总任务数</div>
            <div className="text-2xl font-bold text-blue-700">{stats.total_count}</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="text-sm text-green-600">已完成</div>
            <div className="text-2xl font-bold text-green-700">{stats.completed_count}</div>
          </div>
          <div className="bg-yellow-50 p-3 rounded-lg">
            <div className="text-sm text-yellow-600">上传中</div>
            <div className="text-2xl font-bold text-yellow-700">{stats.uploading_count}</div>
          </div>
          <div className="bg-red-50 p-3 rounded-lg">
            <div className="text-sm text-red-600">失败</div>
            <div className="text-2xl font-bold text-red-700">{stats.failed_count}</div>
          </div>
        </div>
      )}

      {/* 队列状态 */}
      {queueStatus && (
        <div className=" p-3 rounded-lg">
          <div className="text-sm text-gray-600">
            队列状态: {queueStatus.active_count}/{queueStatus.max_concurrent} 活跃, 
            {queueStatus.pending_count} 等待, {queueStatus.paused_count} 暂停
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => cleanupCompleted(7)}
        >
          清理已完成 (7天前)
        </Button>
      </div>

      {/* 任务列表 */}
      <div className="space-y-2">
        {tasks.map(task => (
          <div key={task.id} className="border rounded-lg p-4 ">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 flex-1">
                {getStatusIcon(task.status)}
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">{task.name}</div>
               
                </div>
                <Badge variant={getStatusColor(task.status) as any}>
                  {getStatusText(task.status)}
                </Badge>
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                {/* 操作按钮 */}
                {task.status === UploadTaskStatus.PENDING && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleTaskAction(task.id, 'start')}
                    disabled={isOperating}
                  >
                    <Play className="w-4 h-4" />
                  </Button>
                )}
                
                {task.status === UploadTaskStatus.UPLOADING && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleTaskAction(task.id, 'pause')}
                    disabled={isOperating}
                  >
                    <Pause className="w-4 h-4" />
                  </Button>
                )}
                
                {task.status === UploadTaskStatus.PAUSED && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleTaskAction(task.id, 'resume')}
                    disabled={isOperating}
                  >
                    <Play className="w-4 h-4" />
                  </Button>
                )}
                
                {task.status === UploadTaskStatus.FAILED && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleTaskAction(task.id, 'retry')}
                    disabled={isOperating}
                  >
                    <RotateCcw className="w-4 h-4" />
                  </Button>
                )}
                
                {(task.status === UploadTaskStatus.PENDING || 
                  task.status === UploadTaskStatus.UPLOADING || 
                  task.status === UploadTaskStatus.PAUSED) && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleTaskAction(task.id, 'cancel')}
                    disabled={isOperating}
                  >
                    <Square className="w-4 h-4" />
                  </Button>
                )}
                
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleTaskAction(task.id, 'delete')}
                  disabled={isOperating}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
            
            {/* 进度条 */}
            <div className="text-xs mt-1">
              {(task.progress).toFixed(1)}% - {task.reason && `错误: ${task.reason}`}
            </div>
            
            {/* 错误信息 */}
            {task.status === UploadTaskStatus.FAILED && task.reason && (
              <div className="mt-2 text-sm text-red-600  p-2 rounded">
                错误: {task.reason}
              </div>
            )}
          </div>
        ))}
        
        {tasks.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            暂无上传任务
          </div>
        )}
      </div>
    </div>
  )
}
