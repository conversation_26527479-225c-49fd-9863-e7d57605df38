import { Overlay, OverlayType, SoundOverlay, StickerOverlay } from '@clipnest/remotion-shared/types'
import { IndexableTrack, PlayerDimensions, Track } from '../../types'
import { DraggableState, OverlaysAdjustment } from './drag.context'
import { findOverlay } from '@/modules/video-editor/utils/overlay-helper'
import { generateNewOverlayId, SingleOverlayUpdatePayload } from '@/modules/video-editor/utils/track-helper'
import { DEFAULT_OVERLAY, FPS } from '@/modules/video-editor/constants'
import {
  CloudResourceTypes,
  MaterialResource,
  PasterResource,
  ResourceSource,
  SoundResource,
  StyledTextResource
} from '@/types/resources'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { buildTextOverlay } from '@/modules/video-editor/utils/text'

export type DroppableResource =
  | { resourceType: CloudResourceTypes.MATERIAL, data: MaterialResource.Media & { materialType: ResourceSource } }
  | { resourceType: CloudResourceTypes.PASTER, data: PasterResource.Paster }
  | { resourceType: CloudResourceTypes.SOUND, data: SoundResource.Sound }
  | { resourceType: CloudResourceTypes.STYLED_TEXT, data: StyledTextResource.StyledText }

export type ResourceMeta = {
  type: ResourceType,
  url: string,
  filename?: string,
  customExt?: string
}

export interface ResourceDragInfo {
  isActive: boolean
  resource: DroppableResource & {
    meta: ResourceMeta
  }
  initialMouseX: number

  newOverlay: Overlay
  draggableState: DraggableState | null
  targetTrack: IndexableTrack | null
}

/**
 * 网格对齐函数
 */
export function snapToGrid(value: number) {
  const GRID_SIZE = 1 // 假设帧级别对齐
  return Math.round(value / GRID_SIZE) * GRID_SIZE
}

/**
 * 构建 Overlay 更新数据
 */
export function buildOverlayUpdates(tracks: Track[], overlaysAdjust?: OverlaysAdjustment) {
  if (!overlaysAdjust) return []

  const updates: SingleOverlayUpdatePayload[] = []

  overlaysAdjust.forEach(({
    fromFrameShift = 0,
    durationShift = 0,
    targetStoryboardIndex
  }, itemId) => {
    const overlayToAdjust = findOverlay(tracks, itemId)
    if (overlayToAdjust) {
      updates.push({
        ...overlayToAdjust,
        from: overlayToAdjust.from + fromFrameShift,
        durationInFrames: overlayToAdjust.durationInFrames + durationShift,
        ...(overlayToAdjust.type !== OverlayType.STORYBOARD && targetStoryboardIndex !== undefined && {
          storyboardIndex: targetStoryboardIndex
        })
      })
    }
  })

  return updates
}

const mapMediaTypeToResourceType: Partial<Record<MaterialResource.MediaType, ResourceType>> = {
  [MaterialResource.MediaType.VIDEO]: ResourceType.VIDEO,
  [MaterialResource.MediaType.IMAGE]: ResourceType.STICKER,
  [MaterialResource.MediaType.AUDIO]: ResourceType.SOUND,
}

const mapMediaTypeToOverlayType: Partial<Record<MaterialResource.MediaType, OverlayType>> = {
  [MaterialResource.MediaType.VIDEO]: OverlayType.VIDEO,
  [MaterialResource.MediaType.IMAGE]: OverlayType.STICKER,
  [MaterialResource.MediaType.AUDIO]: OverlayType.SOUND,
}

/**
 * 获取资源元数据
 */
export function getResourceMeta(resource: DroppableResource): ResourceMeta | undefined {
  if (resource.resourceType === CloudResourceTypes.MATERIAL) {
    const type = resource.data.resType !== undefined ? mapMediaTypeToResourceType[resource.data.resType] : undefined

    return type
      ? {
        type,
        url: resource.data.url,
        filename: resource.data.fileName
      }
      : undefined
  }

  if (resource.resourceType === CloudResourceTypes.PASTER) {
    return {
      type: ResourceType.STICKER,
      url: resource.data.content.fileUrl,
    }
  }

  if (resource.resourceType === CloudResourceTypes.SOUND) {
    const url = resource.data.content.itemUrl
    const hasSuffix = url.split('/').pop()?.includes('.')

    return {
      type: hasSuffix ? ResourceType.SOUND : ResourceType.SUFFIXLESS_SOUND,
      url: resource.data.content.itemUrl,
      customExt: !hasSuffix ? '.mp3' : undefined
    }
  }

  if (resource.resourceType === CloudResourceTypes.STYLED_TEXT) {
    return {
      type: ResourceType.FONT,
      url: resource.data.content.fontPath,
    }
  }
}

function materialToOverlay(
  media: MaterialResource.Media,
  from: number,
  tracks: Track[],
  dimension: PlayerDimensions
) {
  // 将秒转换为帧数
  const durationInFrames = snapToGrid((media.duration || 3000) / 1000 * FPS)

  // 根据资源类型确定 Overlay 类型
  const overlayType = media.resType !== undefined ? mapMediaTypeToOverlayType[media.resType] : undefined

  if (!overlayType) {
    return null
  }

  const { width: resourceWidth = dimension.playerWidth, height: resourceHeight = dimension.playerHeight } = media
  const scaleX = dimension.playerWidth / resourceWidth!
  const scaleY = dimension.playerHeight / resourceHeight!
  const usingScale = Math.min(scaleX, scaleY)
  const width = Math.round(resourceWidth! * usingScale)
  const height = Math.round(resourceHeight! * usingScale)

  return {
    ...DEFAULT_OVERLAY,
    width,
    height,
    left: (dimension.playerWidth - width) / 2,
    top: (dimension.playerHeight - height) / 2,
    id: generateNewOverlayId(tracks),
    type: overlayType,
    from,
    durationInFrames,
    originalDurationInFrames: durationInFrames,
    content: media.fileName,
    src: media.url || '',
    styles: overlayType === OverlayType.SOUND ? { volume: 1 } : {}
  } as Overlay
}

/**
 * 从 DroppableResource 构造临时 Overlay 对象用于拖拽预览
 */
export function createOverlayFromResource(
  resource: DroppableResource,
  from: number,
  tracks: Track[],
  dimension: PlayerDimensions
): Overlay | null {
  if (resource.resourceType === CloudResourceTypes.MATERIAL) {
    return materialToOverlay(resource.data, from, tracks, dimension)
  }

  if (resource.resourceType === CloudResourceTypes.PASTER) {
    const width = dimension.playerWidth / 2
    const height = resource.data.content.height / resource.data.content.height * width
    return {
      ...DEFAULT_OVERLAY,
      id: generateNewOverlayId(tracks),
      type: OverlayType.STICKER,
      from,
      durationInFrames: 90,
      content: resource.data.title,
      src: resource.data.content.fileUrl,
      width,
      height,
      left: (dimension.playerWidth - width) / 2,
      top: (dimension.playerHeight - height) / 2,
      styles: {}
    } satisfies StickerOverlay
  }

  if (resource.resourceType === CloudResourceTypes.SOUND) {
    return {
      ...DEFAULT_OVERLAY,
      id: generateNewOverlayId(tracks),
      type: OverlayType.SOUND,
      from,
      durationInFrames: resource.data.content.durationMsec / 1000 * FPS,
      content: resource.data.title,
      src: resource.data.content.itemUrl,
      styles: {
        volume: 1,
      }
    } as SoundOverlay
  }

  if (resource.resourceType === CloudResourceTypes.STYLED_TEXT) {
    return {
      ...buildTextOverlay(resource.data, { textContent: '默认文字' }),
      id: generateNewOverlayId(tracks),
      from,
      width: dimension.playerWidth,
      height: dimension.playerHeight,
    }
  }

  throw new Error(`Unknown resource type: ${(resource as any).resourceType}`)
}
