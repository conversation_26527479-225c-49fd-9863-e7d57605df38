import React, { useCallback, useRef, useState } from 'react'
import { Overlay } from '@clipnest/remotion-shared/types'
import { GhostElement } from '@/modules/video-editor/types'
import {
  DragContext,
  DragContextValues,
  DraggableState,
  DragProviderProps,
  OverlayDragInfo,
  OverlaysAdjustment
} from './drag.context'
import { SingleOverlayUpdatePayload } from '@/modules/video-editor/utils/track-helper'
import { buildOverlayUpdates } from '@/modules/video-editor/contexts/drag/drag.utils'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { getOverlayTrackIndex } from '@/modules/video-editor/utils/overlay-helper'

export const DragProvider: React.FC<DragProviderProps> = ({ children }) => {
  const { tracks, bulkUpdateOverlays } = useEditorContext()

  const [isDragging, setIsDragging] = useState(false)
  const [landingPoint, setLandingPoint] = useState<GhostElement | null>(null)
  const [draggingOverlay, setDraggingOverlay] = useState<Overlay | null>(null)
  const [mousePosition, setMousePosition] = useState<GhostElement | null>(null)
  const [mouseOnCurrentFrame, setMouseOnCurrentFrame] = useState<number | null>(null)
  const [previewOverlaysAdjust, setPreviewOverlaysAdjust] = useState<OverlaysAdjustment>(new OverlaysAdjustment())

  const dragInfoRef = useRef<OverlayDragInfo | null>(null)

  const resetDragState = () => {
    setIsDragging(false)
    setDraggingOverlay(null)
    setLandingPoint(null)
    setMousePosition(null)
    setPreviewOverlaysAdjust(new OverlaysAdjustment())
    setMouseOnCurrentFrame(null)
  }

  const handleTimelineItemAdjustStart = useCallback(
    (overlay: Overlay) => {
      const row = getOverlayTrackIndex(tracks, overlay.id) || 0

      setIsDragging(true)
      setDraggingOverlay(overlay)

      dragInfoRef.current = {
        overlay,
        initialFrom: overlay.from,
        initialDurationInFrames: overlay.durationInFrames,
        initialRow: row,
      }

      setMousePosition({
        from: overlay.from,
        durationInFrames: overlay.durationInFrames,
        row,
        overlay,
      })
      setPreviewOverlaysAdjust(new OverlaysAdjustment())
    },
    [tracks]
  )

  const handleTimelineItemAdjustEnd = useCallback(
    () => {
      const currentDragInfo = dragInfoRef.current

      if (
        !currentDragInfo
        || !currentDragInfo.landingPoint
        || !currentDragInfo.draggableState?.draggable
      ) {
        resetDragState()
        return
      }

      const { overlay: originalOverlay } = currentDragInfo

      if (!originalOverlay) {
        resetDragState()
        return
      }

      const predicatedRow = currentDragInfo.currentRow ?? currentDragInfo.initialRow

      const {
        overlaysAdjust, targetStoryboardIndex, draggable, adjustedStartFrame, adjustedDuration
      } = currentDragInfo.draggableState

      if (!draggable) {
        return resetDragState()
      }

      const itemsToUpdate: SingleOverlayUpdatePayload[] = [
        {
          ...originalOverlay,
          targetTrackIndex: predicatedRow,
          ...(adjustedStartFrame !== undefined && { from: adjustedStartFrame }),
          ...(adjustedDuration && { durationInFrames: adjustedDuration }),
          storyboardIndex: targetStoryboardIndex !== undefined
            ? targetStoryboardIndex
            : originalOverlay.storyboardIndex
        },
        ...buildOverlayUpdates(tracks, overlaysAdjust)
      ]

      bulkUpdateOverlays(itemsToUpdate)
      resetDragState()
    },
    [tracks, dragInfoRef, bulkUpdateOverlays, resetDragState]
  )

  const updateDraggableState  = (newState: DraggableState) => {
    const dragInfo = dragInfoRef.current
    if (!dragInfo) return

    dragInfo.draggableState = newState

    const {
      draggable,
      overlaysAdjust = new OverlaysAdjustment(),
      adjustedStartFrame = dragInfo.initialFrom,
      adjustedDuration = dragInfo.initialDurationInFrames,
      adjustedRow = dragInfo.initialRow
    } = newState

    const mousePosition: GhostElement = {
      overlay: dragInfo.overlay,
      from: adjustedStartFrame,
      durationInFrames: dragInfo.overlay.durationInFrames,
      row: adjustedRow,
      invalid: !draggable
    }

    setMousePosition(mousePosition)

    if (draggable) {
      const newLandingPoint: GhostElement = {
        ...mousePosition,
        from: adjustedStartFrame,
        durationInFrames: adjustedDuration
      }
      setLandingPoint(newLandingPoint)
      dragInfoRef.current!.landingPoint = newLandingPoint
    } else {
      setLandingPoint(null)
    }

    setPreviewOverlaysAdjust(overlaysAdjust)
  }

  return (
    <DragContext.Provider
      value={{
        isDragging,
        draggingOverlay,
        mousePosition,
        landingPoint,
        previewOverlaysAdjust,
        mouseOnCurrentFrame,
        dragInfoRef,

        setIsDragging,
        setMousePosition,
        setLandingPoint,
        setPreviewOverlaysAdjust,
        setMouseOnCurrentFrame,
        resetDragState,
        updateDraggableState,

        handleTimelineItemAdjustStart,
        handleTimelineItemAdjustEnd,
      } as DragContextValues}
    >
      {children}
    </DragContext.Provider>
  )
}
