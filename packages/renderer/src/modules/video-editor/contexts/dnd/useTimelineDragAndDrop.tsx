import React, { RefObject, useCallback, useRef, useState } from 'react'
import { Overlay } from '@clipnest/remotion-shared/types'
import { PIXELS_PER_FRAME } from '@/modules/video-editor/constants'
import { GhostElement, IndexableTrack } from '../../types'
import { DragAction, OverlayDragInfo, OverlaysAdjustment } from './drag.context'
import { DroppableResource } from './dragUtils'
import { useTimelineItemDrag } from './useTimelineItemDrag'
import { useResourceDrag } from './useResourceDrag'

// 这些类型和接口已经移动到 dragContext.ts 和 dragUtils.ts 中
export type TimelineOverlayDnDHook = {
  /**
   * 指示当前是否在进行 Overlay 拖拽
   */
  isDragging: boolean

  dragInfo: RefObject<OverlayDragInfo| null>

  /**
   * 当前拖拽中的 Overlay
   */
  draggingOverlay: Overlay | null

  /**
   * 用于展示鼠标当前拖动位置
   */
  mousePosition: GhostElement | null

  /**
   * 用于指示当前拖拽的 Overlay 的最终落点
   */
  landingPoint: GhostElement | null

  /**
   * 用于预览即将会发生的 Overlay 飘移
   */
  previewOverlaysAdjust: OverlaysAdjustment

  /**
   * 指示当前鼠标指向的时间点
   */
  mouseOnCurrentFrame: number | null

  handleOverlayDragStart(overlay: Overlay, action: DragAction): void
  handleOverlayDragMove(deltaX: number, targetTrackIndex?: number): void
  handleOverlayDragEnd(): void

  handleResourceDragStart(resource: DroppableResource, initialMouseX: number): void
  handleResourceDragMove(currentMouseX: number, targetTrack?: IndexableTrack): void
  handleResourceDragEnd(): void

  handleMouseMove(e: React.MouseEvent<HTMLDivElement>): void
  handleOverlayDragOverBound(): void
}

/**
 * Custom hook to manage the state related to timeline drag and drop operations.
 * 为了保持向后兼容，这个 hook 保持原有的 API 接口，内部使用独立的状态管理
 */
export const useTimelineDragAndDrop = (
  timelineGridRef: RefObject<HTMLDivElement | null>,
  zoomScale: number,
): TimelineOverlayDnDHook => {
  // 为了向后兼容，这里使用独立的状态管理，不依赖 Context
  const dragInfo = useRef<OverlayDragInfo | null>(null)

  // 使用拆分的 hook，但需要提供独立的状态管理
  const timelineItemDrag = useTimelineItemDrag(zoomScale)
  const resourceDrag = useResourceDrag(timelineGridRef, zoomScale)

  // 独立的状态管理（不使用 Context）
  const [isDragging, setIsDragging] = useState(false)
  const [draggingOverlay, setDraggingOverlay] = useState<Overlay | null>(null)
  const [mousePosition, setMousePosition] = useState<GhostElement | null>(null)
  const [landingPoint, setLandingPoint] = useState<GhostElement | null>(null)
  const [previewOverlaysAdjust, setPreviewOverlaysAdjust] = useState<OverlaysAdjustment>(new OverlaysAdjustment())
  const [mouseOnCurrentFrame, setMouseOnCurrentFrame] = useState<number | null>(null)

  const resetDragState = useCallback(() => {
    setIsDragging(false)
    setDraggingOverlay(null)
    setLandingPoint(null)
    setMousePosition(null)
    setPreviewOverlaysAdjust(new OverlaysAdjustment())
    setMouseOnCurrentFrame(null)
    dragInfo.current = null
  }, [])

  const handleMouseMove = useCallback<TimelineOverlayDnDHook['handleMouseMove']>(
    event => {
      const { clientX } =  event
      const rect = timelineGridRef.current?.getBoundingClientRect()
      if (rect) {
        setMouseOnCurrentFrame((clientX - rect.left) / PIXELS_PER_FRAME / zoomScale)
      }
      return
    },
    [zoomScale],
  )

  const handleOverlayDragOverBound = useCallback(() => {
    setMouseOnCurrentFrame(null)
    if (isDragging) {
      resetDragState()
    }
  }, [isDragging, resetDragState])

  return {
    isDragging,
    draggingOverlay,
    dragInfo,
    mousePosition,
    landingPoint,
    previewOverlaysAdjust,
    mouseOnCurrentFrame,

    handleMouseMove,
    handleOverlayDragOverBound,

    // TimelineItem 拖拽方法
    handleOverlayDragStart: timelineItemDrag.handleOverlayDragStart,
    handleOverlayDragMove: timelineItemDrag.handleOverlayDragMove,
    handleOverlayDragEnd: timelineItemDrag.handleOverlayDragEnd,

    // 资源拖拽方法
    handleResourceDragStart: resourceDrag.handleResourceDragStart,
    handleResourceDragMove: resourceDrag.handleResourceDragMove,
    handleResourceDragEnd: resourceDrag.handleResourceDragEnd
  }
}

// 导出拆分的 hook 和 context，供高级用户使用
export { useDragContext } from './drag.context'
export { useTimelineItemDrag } from './useTimelineItemDrag'
export { useResourceDrag } from './useResourceDrag'

export { DragProvider } from '@/modules/video-editor/contexts/timeline/drag.provider'
