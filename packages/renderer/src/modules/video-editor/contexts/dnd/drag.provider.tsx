import React, { useState } from 'react'
import { Overlay } from '@clipnest/remotion-shared/types'
import { GhostElement } from '@/modules/video-editor/types'
import { DragContext, DragProviderProps, OverlaysAdjustment } from './drag.context'

export const DragProvider: React.FC<DragProviderProps> = ({ children, dragInfo }) => {
  const [isDragging, setIsDragging] = useState(false)
  const [draggingOverlay, setDraggingOverlay] = useState<Overlay | null>(null)
  const [mousePosition, setMousePosition] = useState<GhostElement | null>(null)
  const [landingPoint, setLandingPoint] = useState<GhostElement | null>(null)
  const [previewOverlaysAdjust, setPreviewOverlaysAdjust] = useState<OverlaysAdjustment>(new OverlaysAdjustment())
  const [mouseOnCurrentFrame, setMouseOnCurrentFrame] = useState<number | null>(null)

  const resetDragState = () => {
    setIsDragging(false)
    setDraggingOverlay(null)
    setLandingPoint(null)
    setMousePosition(null)
    setPreviewOverlaysAdjust(new OverlaysAdjustment())
    setMouseOnCurrentFrame(null)
    dragInfo.current = null
  }

  return (
    <DragContext.Provider
      value={{
      // State
        isDragging,
        draggingOverlay,
        mousePosition,
        landingPoint,
        previewOverlaysAdjust,
        mouseOnCurrentFrame,
        dragInfo,

        // Actions
        setIsDragging,
        setDraggingOverlay,
        setMousePosition,
        setLandingPoint,
        setPreviewOverlaysAdjust,
        setMouseOnCurrentFrame,
        resetDragState,
      }}
    >
      {children}
    </DragContext.Provider>
  )
}
