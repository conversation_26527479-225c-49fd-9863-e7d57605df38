import { RefObject, useCallback, useRef } from 'react'
import { Overlay, TextOverlay } from '@clipnest/remotion-shared/types'
import { useEditorContext } from '../editor/context'
import { PIXELS_PER_FRAME } from '@/modules/video-editor/constants'
import {
  calculateTracksAfterOverlayUpdated,
  findStoryboardByFromFrame,
  isOverlayAcceptableByTrack
} from '@/modules/video-editor/utils/track-helper'
import { IndexableTrack } from '../../types'
import { DraggableState, OverlaysAdjustment, useDragContext } from './drag.context'
import {
  buildOverlayUpdates,
  createOverlayFromResource,
  DroppableResource,
  getResourceMeta,
  ResourceDragInfo,
  snapToGrid
} from './dragUtils'
import { cacheManager } from '@/libs/cache/cache-manager'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { toast } from 'react-toastify'
import { calculateTextRenderInfo } from '@clipnest/overlay-renderer'
import {
  byStartFrame,
  byTypeIfInNarrationTrack,
  findOverlaysBetweenFrames
} from '@/modules/video-editor/utils/overlay-helper'

// 简化版的计算拖拽状态逻辑，专门用于资源拖拽
const useCalculateDraggableStateForResource = () => {
  const { tracks } = useEditorContext()

  return useCallback((
    currentOverlay: Overlay,
    intendedNewFrom: number,
    targetTrack: IndexableTrack,
  ): DraggableState => {
    if (!currentOverlay || !isOverlayAcceptableByTrack(currentOverlay, targetTrack)) {
      return {
        draggable: false,
      }
    }

    let adjustedStartFrame = intendedNewFrom
    const overlaysAdjust = new OverlaysAdjustment()

    const targetStoryboard = targetTrack.isGlobalTrack
      ? null
      : findStoryboardByFromFrame(tracks, intendedNewFrom)

    // 处理移动到全局轨道的情况
    const targetOverlays = targetTrack.overlays
      .filter(o => o.id !== currentOverlay.id)
      .filter(byTypeIfInNarrationTrack(currentOverlay, targetTrack))

    const leftOverlapping = findOverlaysBetweenFrames(
      targetOverlays,
      -1,
      intendedNewFrom,
      'start',
    ).filter(o => o.from + o.durationInFrames > intendedNewFrom)
      .sort(byStartFrame('desc'))
      .at(0)

    // 左侧有重叠的情况下，则以重叠元素的结尾帧作为拖动的新起始帧
    if (leftOverlapping) {
      adjustedStartFrame = leftOverlapping.from + leftOverlapping.durationInFrames
    }

    const rightOverlapping = findOverlaysBetweenFrames(
      targetOverlays,
      adjustedStartFrame - 1,
      Infinity,
      'start'
    ).filter(o => o.from < adjustedStartFrame + currentOverlay.durationInFrames)
      .sort(byStartFrame())
      .at(0)

    // 右侧有重叠的情况下, 则将重叠元素向后推移
    if (rightOverlapping) {
      const affectedOverlays = findOverlaysBetweenFrames(
        targetOverlays,
        rightOverlapping.from - 1,
        Infinity,
        'start'
      )

      const overlapSize = adjustedStartFrame + currentOverlay.durationInFrames - rightOverlapping.from

      affectedOverlays.forEach(o => {
        overlaysAdjust.set(o.id, { fromFrameShift: overlapSize })
      })
    }

    return {
      draggable: true,
      overlaysAdjust,
      adjustedStartFrame,
      targetStoryboardIndex: targetStoryboard?.index
    }
  }, [tracks])
}

export interface ResourceDragHook {
  handleResourceDragStart(resource: DroppableResource, initialMouseX: number): void
  handleResourceDragMove(currentMouseX: number, targetTrack?: IndexableTrack): void
  handleResourceDragEnd(): void
}

/**
 * 资源区拖拽逻辑 Hook
 */
export const useResourceDrag = (
  timelineGridRef: RefObject<HTMLDivElement | null>,
  zoomScale: number,
): ResourceDragHook => {
  const {
    tracks, getPlayerDimensions, updateTracks
  } = useEditorContext()

  const {
    setIsDragging,
    setMousePosition,
    setLandingPoint,
    setPreviewOverlaysAdjust,
    resetDragState
  } = useDragContext()

  const calculateDraggableState = useCalculateDraggableStateForResource()
  const resourceDragInfo = useRef<ResourceDragInfo | null>(null)

  const handleResourceDragStart = useCallback(
    (resource: DroppableResource, initialMouseX: number) => {
      const meta = getResourceMeta(resource)

      if (!meta) {
        toast('无效的资源类型')
        return
      }

      const newOverlay = createOverlayFromResource(resource, 0, tracks, getPlayerDimensions())
      if (!newOverlay) {
        toast('无效的资源类型')
        return
      }

      resourceDragInfo.current = {
        isActive: true,
        resource: {
          ...resource,
          meta
        },
        initialMouseX,
        newOverlay,
        targetTrack: null,
        draggableState: null,
      }

      // 拖动开始时预下载资源, 避免拖动到轨道上时的卡顿
      if (meta.type === ResourceType.FONT) {
        void cacheManager.font.cacheFont(meta.url)
      } else {
        void cacheManager.resource.cacheResource(
          meta.type,
          meta.url,
          undefined,
          meta.customExt || meta.filename?.split('.')?.pop()
        )
      }

      setIsDragging(true)
      setMousePosition(null)
      setLandingPoint(null)
      setPreviewOverlaysAdjust(new OverlaysAdjustment())
    },
    [tracks, getPlayerDimensions, setIsDragging, setMousePosition, setLandingPoint, setPreviewOverlaysAdjust]
  )

  const handleResourceDragMove = useCallback(
    (currentMouseX: number, targetTrack?: IndexableTrack) => {
      if (!resourceDragInfo.current || !timelineGridRef.current) return

      // 不在轨道上，清除预览
      if (!targetTrack) {
        setMousePosition(null)
        setLandingPoint(null)
        setPreviewOverlaysAdjust(new OverlaysAdjustment())
        resourceDragInfo.current = {
          ...resourceDragInfo.current,
          targetTrack: null
        }
        return
      }

      const rect = timelineGridRef.current.getBoundingClientRect()
      const intendedNewFrom = snapToGrid((currentMouseX - rect.left) / zoomScale / PIXELS_PER_FRAME)
      const newOverlay: Overlay = {
        ...resourceDragInfo.current.newOverlay,
        from: intendedNewFrom
      }

      const draggableState = calculateDraggableState(
        newOverlay,
        intendedNewFrom,
        targetTrack,
      )

      resourceDragInfo.current = {
        ...resourceDragInfo.current,
        targetTrack,
        draggableState
      }

      const { overlaysAdjust, adjustedDuration, adjustedStartFrame, draggable } = draggableState

      const newMousePosition = {
        overlay: newOverlay,
        from: intendedNewFrom,
        durationInFrames: newOverlay.durationInFrames,
        row: targetTrack.index,
        invalid: !draggable
      }
      setMousePosition(newMousePosition)

      if (draggable) {
        const newLandingPoint = {
          ...newMousePosition,
          ...(adjustedStartFrame !== undefined && { from: adjustedStartFrame }),
          ...(adjustedDuration !== undefined && { durationInFrames: adjustedDuration }),
        }
        setLandingPoint(newLandingPoint)
      } else {
        setLandingPoint(null)
      }

      setPreviewOverlaysAdjust(overlaysAdjust || new OverlaysAdjustment())
    },
    [zoomScale, calculateDraggableState, setMousePosition, setLandingPoint, setPreviewOverlaysAdjust]
  )

  const handleResourceDragEnd = useCallback(
    async () => {
      if (!resourceDragInfo.current) {
        return resetDragState()
      }

      const { draggableState, targetTrack, newOverlay, resource } = resourceDragInfo.current
      if (!resourceDragInfo.current || !draggableState || !targetTrack || !newOverlay || !resource) {
        return resetDragState()
      }

      const {
        adjustedDuration, adjustedStartFrame, targetStoryboardIndex, draggable, overlaysAdjust
      } = draggableState

      if (!draggable) return resetDragState()

      // TODO: 在此处显示一个 Loading
      const localSrc = resource && resource.meta
        ? await cacheManager.resource.waitForCachedResource(resource.meta.type, resource.meta.url)
        : undefined

      if (!localSrc) {
        toast('资源加载失败')
        return resetDragState()
      }

      const finalOverlay: Overlay = {
        ...newOverlay,
        localSrc,
        from: adjustedStartFrame ?? newOverlay.from,
        durationInFrames: adjustedDuration ?? newOverlay.durationInFrames,
        ...(targetStoryboardIndex !== undefined && { storyboardIndex: targetStoryboardIndex })
      }

      if (resource.meta.type === ResourceType.FONT) {
        const font = await cacheManager.font.cacheFont(resource.meta.url)

        if (!font) throw new Error('字体加载失败')

        const dimension = getPlayerDimensions()
        const { minWidth, minHeight } = calculateTextRenderInfo(font, finalOverlay as TextOverlay)
        finalOverlay.width = minWidth
        finalOverlay.height = minHeight
        finalOverlay.left = (dimension.playerWidth - minWidth) / 2
        finalOverlay.top = (dimension.playerHeight - minHeight) / 2
      }

      const updates = buildOverlayUpdates(tracks, overlaysAdjust)

      updateTracks(prevTracks => {
        const inserted = prevTracks.map((track, trackIndex) => {
          if (trackIndex === targetTrack.index) {
            return {
              ...track,
              overlays: [...track.overlays, finalOverlay]
            }
          }

          return track
        })

        return updates.reduce(
          (result, update) => calculateTracksAfterOverlayUpdated(result, update),
          inserted
        )
      })

      return resetDragState()
    },
    [tracks, updateTracks, getPlayerDimensions, resetDragState]
  )

  return {
    handleResourceDragStart,
    handleResourceDragMove,
    handleResourceDragEnd
  }
}
