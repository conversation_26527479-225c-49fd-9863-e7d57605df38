import React, { ReactNode, useCallback, useEffect, useRef, useState } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { cn } from '@/components/lib/utils'
import { ImageOff, Pause, Play } from 'lucide-react'
import { AudioProgress } from '@/modules/video-editor/components/common/audio-progress-bar'
import { useResourceCacheStatus } from '../../hooks/resource/useResourceCacheStatus'
import { NativeAudioManager } from './audio-resource-item'
import { getSrcPath } from '../../hooks/useAddAudioToTimeLine'
import { useParseUrlFromObjectHref } from '@/hooks/useParseUrlFromObjectHref'
export interface LocalAudioResourceItemProps {
  /**
   * 音频地址
   */
  audioUrl: string
  /**
   * 音频时长
   */
  durations: number
  /**
   * 资源名
   */
  title: string
  /**
   * 资源id
   */
  id: string
  /**
   * 封面图
   */
  coverUrl?: string
  /**
   * 默认图标
   */
  icon?: ReactNode

  /**
   * 自定义内容
   */
  children?: ReactNode
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 资源类型，用于检查本地缓存
   */
  resourceType: ResourceType
  /**
   * 自定义扩展名
   */
  customExt?: string
  /**
   * 是否是本地资源，非本地资源url需要重新获取
   */
  isLocal?: boolean
}

export function LocalAudioResourceItem({
  audioUrl,
  durations,
  title,
  id,
  coverUrl,
  icon,
  children,
  className = '',
  resourceType,
  isLocal = true,
}: LocalAudioResourceItemProps) {
  const { data: parsedUrl } = useParseUrlFromObjectHref(audioUrl)
  const resolvedAudioUrl = isLocal ? audioUrl : parsedUrl || audioUrl

  const thumbnailUrl = coverUrl || ''
  const description = (durations / 1000).toFixed(1) + 's'

  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [audioInitialized, setAudioInitialized] = useState(false)
  const [imageLoadError, setImageLoadError] = useState(false)
  const [showProgressBar, setShowProgressBar] = useState(false)

  const audioRef = useRef<HTMLAudioElement | null>(null)
  const audioManager = NativeAudioManager.getInstance()

  const { isCached } = useResourceCacheStatus(resourceType, resolvedAudioUrl)

  // 音频事件处理函数
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current && audioRef.current.duration) {
      setCurrentTime(audioRef.current.currentTime)
    }
  }, [])

  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current && audioRef.current.duration) {
      setDuration(audioRef.current.duration)
    }
  }, [])

  const handleAudioEnded = useCallback(() => {
    setIsPlaying(false)
    setCurrentTime(0)
    setShowProgressBar(false)
    audioManager.stop(id)
  }, [id])

  const handleAudioError = useCallback(() => {
    setIsPlaying(false)
    setShowProgressBar(false)
    console.error('音频播放错误')
  }, [])

  // 音频管理器事件监听器
  const handleAudioManagerEvent = useCallback((event: 'play' | 'pause' | 'stop') => {
    switch (event) {
      case 'stop':
        setIsPlaying(false)
        setShowProgressBar(false)
        if (audioRef.current) {
          audioRef.current.currentTime = 0
        }
        setCurrentTime(0)
        break
      case 'pause':
        setIsPlaying(false)
        break
      case 'play':
        setIsPlaying(true)
        setShowProgressBar(true)
        break
    }
  }, [])

  // 初始化音频元素
  const initializeAudio = useCallback(() => {
    if (audioInitialized || !resolvedAudioUrl) return null

    const audio = new Audio()
    audio.preload = 'metadata'
    audioRef.current = audio

    // 添加音频事件监听器
    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('ended', handleAudioEnded)
    audio.addEventListener('error', handleAudioError)

    audio.src = getSrcPath(resolvedAudioUrl)

    // 注册到音频管理器
    audioManager.registerListener(id, handleAudioManagerEvent)
    setAudioInitialized(true)

    return audio
  }, [resolvedAudioUrl, resourceType, isCached])

  // 播放/暂停切换
  const handleTogglePlay = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation()

      // 如果音频尚未初始化，先初始化
      if (!audioInitialized) {
        const audio = initializeAudio()
        if (!audio) return
      }

      if (!audioRef.current) return

      if (isPlaying) {
        audioManager.pause(id)
        setIsPlaying(false)
        setShowProgressBar(false)
      } else {
        audioManager.play(audioRef.current, id)
        setIsPlaying(true)
        setShowProgressBar(true)
      }
    },
    [audioInitialized, initializeAudio, isPlaying, id],
  )

  // 组件清理
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        // 停止播放
        audioRef.current.pause()
        audioRef.current.src = ''
        audioRef.current.load()

        // 移除事件监听器
        audioRef.current.removeEventListener('timeupdate', handleTimeUpdate)
        audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata)
        audioRef.current.removeEventListener('ended', handleAudioEnded)
        audioRef.current.removeEventListener('error', handleAudioError)

        // 从音频管理器中注销
        audioManager.unregisterListener(id)
        audioManager.stop(id)
      }
    }
  }, [handleTimeUpdate, handleLoadedMetadata, handleAudioEnded, handleAudioError, id])

  return (
    <div className={`aspect-square relative ${className}`}>
      <div
        className={cn(
          `group w-full h-full
          bg-muted/30
          rounded dark:bg-gray-800/40
          border dark:border-gray-700/10
          hover:border-blue-500/20 dark:hover:border-blue-500/20
          hover:bg-blue-500/5 dark:hover:bg-blue-500/5
          transition-all overflow-hidden`,
          isCached ? 'border-green-500/20 dark:border-green-500/20' : '',
          resourceType && resolvedAudioUrl ? 'cursor-grab active:cursor-grabbing' : '',
        )}
        style={{ zIndex: 1, position: 'relative' }}
      >
        {description && isLocal && (
          <div className="text-[10px] border border-neutral-800 bg-neutral-900/80 rounded px-1 h-4 flex items-center justify-center text-muted absolute bottom-2 left-2 z-2">
            {description}
          </div>
        )}

        {thumbnailUrl ? (
          <div className="absolute inset-0 flex items-center justify-center">
            {imageLoadError ? (
              <div className="flex items-center justify-center text-gray-400">
                <ImageOff className="w-8 h-8" />
              </div>
            ) : (
              <img
                src={thumbnailUrl}
                alt={title}
                className="max-w-full max-h-full object-contain"
                loading="lazy"
                onError={() => setImageLoadError(true)}
                onLoad={() => setImageLoadError(false)}
              />
            )}
          </div>
        ) : (
          icon && <div className="absolute inset-0 flex items-center justify-center text-gray-400">{icon}</div>
        )}

        {/* 播放/暂停按钮 - 悬浮显示，播放时默认显示 */}
        {resolvedAudioUrl && (
          <button
            onClick={handleTogglePlay}
            className={cn(
              'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
              'rounded-full p-3 shadow-lg',
              'bg-black/70 hover:bg-black/90 text-white',
              'transition-all duration-200 z-30',
              'opacity-0 group-hover:opacity-100',
              isPlaying && 'opacity-100',
            )}
          >
            {isPlaying ? <Pause size={20} /> : <Play size={20} />}
          </button>
        )}

        {/* 竖直进度指示器 - 跟随播放进度从左到右移动 */}
        {(showProgressBar || isPlaying) && resolvedAudioUrl && duration > 0 && (
          <div
            className="absolute top-0 bottom-0 w-0.5 bg-blue-500 z-25 transition-all duration-100"
            style={{
              left: `${duration > 0 ? (currentTime / duration) * 100 : 0}%`,
              boxShadow: '0 0 4px rgba(59, 130, 246, 0.5)',
            }}
          />
        )}

        {/* 播放进度遮罩 */}
        {(showProgressBar || isPlaying) && resolvedAudioUrl && duration > 0 && (
          <AudioProgress currentTime={currentTime} duration={duration} isPlaying={isPlaying} className="" />
        )}
      </div>

      {/* 子内容 */}
      {children}
    </div>
  )
}
