import { useCallback } from 'react'
import { CaptionOverlay, Overlay, OverlayType, TextOverlay } from '@clipnest/remotion-shared/types'
import { useEditorContext } from '@/modules/video-editor/contexts'
import {
  DEFAULT_OVERLAY,
  DEFAULT_TEXT_OVERLAY_STYLES,
  FPS,
  TEXT_DEFAULT_CLOUD_FONT_SRC
} from '@/modules/video-editor/constants'
import {
  findLastOverlay,
  getOverlayTimeRange,
  getOverlayTrackIndex,
  getStoryboardAt
} from '@/modules/video-editor/utils/overlay-helper'
import { cloneDeep } from 'lodash'
import {
  calculateTrackAfterOverlayPushed,
  calculateTrackAfterOverlayRemoved,
  findStoryboardByFromFrame,
  generateNewOverlayId,
  isOverlayAcceptableByTrack,
  MAP_OVERLAY_TO_TRACK
} from '@/modules/video-editor/utils/track-helper'
import { toast } from 'react-toastify'
import { TrackType } from '@/modules/video-editor/types'

type AddOverlayPayload = {}
  & Pick<Partial<Overlay>, keyof typeof DEFAULT_OVERLAY>
  & Omit<Overlay, keyof typeof DEFAULT_OVERLAY | 'id'>

export type OverlayHelper = {
  /**
   * 向全局轨道添加 Overlay
   */
  addOverlayToGlobalTrack(overlay: AddOverlayPayload): void

  /**
   * 向指定轨道追加 overlay. 当 `startFrame` 不为空时, 则追加到其所指位置; 否则追加到轨道末尾
   */
  appendOverlayToTrack(
    trackIndex: number,
    overlay: Omit<AddOverlayPayload, 'from'>,
    startFrame?: number
  ): void

  /**
   * 在指定帧将 overlay 分割为两个独立的 overlay
   * 用于在视频/音频内容中创建剪切或过渡
   */
  splitOverlay(id: number, splitPosition: number): void

  /**
   * 处理视频速度和时长的变化
   */
  changeOverlayPlaySpeed(id: number, speed: number, newDuration: number): void

  /**
   * 移动 overlay 到指定的分镜
   */
  moveOverlayToStoryboardIndexAt(overlay: Overlay, targetStoryboardIndex: number): void

  /**
   * 移动指定位置上的 Track 到新的位置
   */
  moveTrack(fromIndex: number, toIndex: number): void

  /**
   * 为口播轨道生成默认的 TextOverlay
   */
  generateDefaultCaptionOverlay(opts?: Partial<TextOverlay>): TextOverlay

  /**
   * 在指定轨道的指定位置添加 overlay
   */
  addOverlayToTrack(trackIndex: number, payload: Omit<AddOverlayPayload, 'from'>): void
}

/**
 * 计算分割媒体 overlay 后半部分的开始时间
 * 对于视频片段和音频，我们需要调整它们的内部开始时间
 * 以在分割后保持连续性
 */
const calculateSecondHalfStartTime = (
  overlay: Overlay,
  firstPartDuration: number
): number => {
  if (overlay.type === OverlayType.VIDEO) {
    return (overlay.videoStartTime || 0) + firstPartDuration
  }
  if (overlay.type === OverlayType.SOUND) {
    return (overlay.startFromSound || 0) + firstPartDuration
  }
  return 0
}

/**
 * 从原始 overlay 分割时创建两个新的 overlay 对象
 * 前半部分保持原始 ID 和时间
 * 后半部分获得新的 ID 和调整后的时间属性
 * 保留原始 overlay 的所有其他属性
 */
const createSplitOverlays = (
  original: Overlay,
  newId: number,
  splitFrame: number,
  firstPartDuration: number,
  secondPartDuration: number,
  secondHalfStartTime: number
): [Overlay, Overlay] => {
  const msPerFrame = 1000 / FPS
  const splitTimeMs = splitFrame * msPerFrame

  if (original.type === OverlayType.CAPTION) {
    // 计算两个分割部分的绝对时间范围
    const originalStartMs = original.from * msPerFrame
    const splitOffsetMs = splitTimeMs - originalStartMs // 相对于 overlay 开始的时间

    // 在单词级别分割字幕，保持时间戳相对于其 overlay
    const firstHalfCaptions = original.captions
      .filter(caption => caption.startMs < splitOffsetMs)
      .map(caption => ({
        ...caption,
        endMs: Math.min(caption.endMs, splitOffsetMs),
        words: caption.words
          .filter(word => word.startMs < splitOffsetMs)
          .map(word => ({
            ...word,
            endMs: Math.min(word.endMs, splitOffsetMs)
          }))
      }))
      .filter(caption => caption.words.length > 0)
      .map(caption => ({
        ...caption,
        text: caption.words.map(w => w.word).join(' ')
      }))

    const secondHalfCaptions = original.captions
      .filter(caption => caption.endMs > splitOffsetMs)
      .map(caption => ({
        ...caption,
        startMs: Math.max(0, caption.startMs - splitOffsetMs),
        endMs: caption.endMs - splitOffsetMs,
        words: caption.words
          .filter(word => word.endMs > splitOffsetMs)
          .map(word => ({
            ...word,
            startMs: Math.max(0, word.startMs - splitOffsetMs),
            endMs: word.endMs - splitOffsetMs
          }))
      }))
      .filter(caption => caption.words.length > 0)
      .map(caption => ({
        ...caption,
        text: caption.words.map(w => w.word).join(' ')
      }))

    // 创建带有调整后字幕的分割 overlay
    const firstHalf: CaptionOverlay = {
      ...original,
      durationInFrames: firstPartDuration,
      captions: firstHalfCaptions
    }

    const secondHalf: CaptionOverlay = {
      ...original,
      id: newId,
      from: splitFrame,
      durationInFrames: secondPartDuration,
      captions: secondHalfCaptions
    }

    return [firstHalf, secondHalf]
  }

  const firstHalf: Overlay = {
    ...original,
    durationInFrames: firstPartDuration
  }

  const secondHalf: Overlay = {
    ...original,
    id: newId,
    from: splitFrame,
    durationInFrames: secondPartDuration,
    ...(original.type === OverlayType.VIDEO && {
      videoStartTime: secondHalfStartTime
    }),
    ...(original.type === OverlayType.SOUND && {
      startFromSound: secondHalfStartTime
    })
  }

  return [firstHalf, secondHalf]
}

export const useOverlayHelper = (): OverlayHelper => {
  const {
    tracks,
    updateTracks,
    setSelectedOverlay,
    videoPlayer: { seekTo },
    getPlayerDimensions
  } = useEditorContext()

  const splitOverlay = (id: number, splitFrame: number) => {
    return updateTracks(prevTracks => {
      // 找到要分割的 overlay 及其所在的 track
      let overlayToSplit: Overlay | undefined
      let trackIndex = -1

      for (let i = 0; i < prevTracks.length; i++) {
        const foundOverlay = prevTracks[i].overlays.find(o => o.id === id)
        if (foundOverlay) {
          overlayToSplit = foundOverlay
          trackIndex = i
          break
        }
      }

      if (!overlayToSplit || trackIndex === -1) {
        console.warn('Overlay not found:', id)
        return prevTracks
      }

      // 验证分割点
      if (
        splitFrame <= overlayToSplit.from
        || splitFrame >= overlayToSplit.from + overlayToSplit.durationInFrames
      ) {
        console.warn('Invalid split point')
        return prevTracks
      }

      const firstPartDuration = splitFrame - overlayToSplit.from
      const secondPartDuration = overlayToSplit.durationInFrames - firstPartDuration

      // 计算新 ID
      const allOverlays = prevTracks.map(t => t.overlays).flat()
      const newId = Math.max(...allOverlays.map(o => o.id)) + 1

      // 计算媒体 overlay 的开始时间
      const secondHalfStartTime = calculateSecondHalfStartTime(
        overlayToSplit,
        firstPartDuration
      )

      // 创建分割后的 overlays
      const [firstHalf, secondHalf] = createSplitOverlays(
        overlayToSplit,
        newId,
        splitFrame,
        firstPartDuration,
        secondPartDuration,
        secondHalfStartTime
      )

      // 更新 tracks
      return prevTracks.map((track, index) => {
        if (index !== trackIndex) return track

        // 替换原始 overlay 并添加新的 overlay
        const updatedOverlays = track.overlays.map(overlay =>
          overlay.id === id ? firstHalf : overlay
        )

        return {
          ...track,
          overlays: [...updatedOverlays, secondHalf]
        }
      })
    })
  }

  const changeOverlayPlaySpeed = (overlayId: number, speed: number, newDuration: number) => {
    return updateTracks(prevTracks => {
      // 找到要修改的 overlay
      let overlayToUpdate: Overlay | null = null
      let trackIndex = -1

      for (let i = 0; i < prevTracks.length; i++) {
        const foundOverlay = prevTracks[i].overlays.find(o => o.id === overlayId)
        if (foundOverlay) {
          overlayToUpdate = foundOverlay
          trackIndex = i
          break
        }
      }

      if (!overlayToUpdate || trackIndex === -1) return prevTracks

      // 创建更新后的 overlay
      const updatedOverlay = {
        ...overlayToUpdate,
        speed,
        durationInFrames: newDuration
      }

      // TODO: 检查重叠并调整其他 overlays
      return [
        ...prevTracks.slice(0, trackIndex),
        {
          ...prevTracks[trackIndex],
          overlays: prevTracks[trackIndex].overlays.map(o =>
            o.id === overlayId ? updatedOverlay : o
          )
        },
        ...prevTracks.slice(trackIndex + 1)
      ]
    })
  }

  const moveOverlayToStoryboardIndexAt = (targetOverlay: Overlay, targetStoryboardIndex: number) => {
    if (targetOverlay.type !== OverlayType.VIDEO) {
      console.warn('当前暂时只支持移动视频 Overlay')
      return
    }

    return updateTracks(prevTracks => {
      const targetStoryboard = getStoryboardAt(prevTracks, targetStoryboardIndex)
      if (!targetStoryboard) {
        console.warn('目标分镜不存在')
        return prevTracks
      }

      const clonedTracks = cloneDeep(prevTracks)

      // 从原始轨道中移除目标 Overlay, 并前移后续 Overlay
      const sourceTrackIndex = getOverlayTrackIndex(clonedTracks, targetOverlay.id)
      clonedTracks[sourceTrackIndex] = calculateTrackAfterOverlayRemoved(
        clonedTracks,
        sourceTrackIndex,
        targetOverlay
      )

      // 寻找允许插入的轨道
      const targetTrack = clonedTracks
        .filter(t => t.type === TrackType.VIDEO)
        .filter(t => t.overlays.every(o => o.storyboardIndex !== targetStoryboardIndex))
        .at(0)

      if (targetTrack) {
        targetTrack.overlays.push({
          ...targetOverlay,
          storyboardIndex: targetStoryboardIndex,
          from: targetStoryboard.from
        })

        return clonedTracks
      }

      // 找不到可用轨道, 则创建新的轨道并插入
      const lastVideoTrackIndex = [...clonedTracks]
        .reverse()
        .findIndex(t => t.type === TrackType.VIDEO && !t.isGlobalTrack)

      const newTrackIndex = lastVideoTrackIndex !== -1
        ? clonedTracks.length - lastVideoTrackIndex
        : 1

      clonedTracks.splice(newTrackIndex, 0, {
        type: TrackType.VIDEO,
        overlays: [{
          ...targetOverlay,
          from: targetStoryboard.from,
          storyboardIndex: targetStoryboardIndex
        }]
      })

      return clonedTracks
    })
  }

  const appendOverlayToTrack = (trackIndex: number, payload: Omit<AddOverlayPayload, 'from'>, startFrame?: number) => {
    updateTracks(prevTracks => (
      prevTracks.map((track, index) => {
        if (index !== trackIndex) return track

        const [newTrack, newOverlay] = calculateTrackAfterOverlayPushed(prevTracks, trackIndex, payload, startFrame)

        if (newOverlay) {
          setSelectedOverlay(newOverlay)
          seekTo(newOverlay.from)
        }

        return newTrack
      })
    ))
  }

  const addOverlayToTrack = (trackIndex: number, payload: AddOverlayPayload) => {
    updateTracks(prevTracks => {
      const storyboard = findStoryboardByFromFrame(prevTracks, payload.from)

      return (
        prevTracks.map((track, index) => {
          if (index !== trackIndex) return track

          if (!isOverlayAcceptableByTrack(payload, track)) {
            toast('目标轨道不支持该类型的素材', { type: 'error' })
            return track
          }

          const newOverlay = {
            ...DEFAULT_OVERLAY,
            ...payload,
            id: generateNewOverlayId(prevTracks),
            ...(storyboard && { storyboardIndex: storyboard?.index })
          } as Overlay

          setSelectedOverlay(newOverlay)
          seekTo(newOverlay.from)

          return {
            ...track,
            overlays: [...track.overlays, newOverlay]
          }
        })
      )
    })
  }

  const addOverlayToGlobalTrack = (overlay: AddOverlayPayload) => {
    updateTracks(prevTracks => {
      const targetTrackType = MAP_OVERLAY_TO_TRACK[overlay.type]
      const targetTrackIndex = prevTracks.findIndex(t => t.isGlobalTrack && t.type === targetTrackType)
      const id = generateNewOverlayId(prevTracks)

      // 找不到合适的全局轨道, 则自动创建一条并添加
      if (targetTrackIndex === -1) {
        return [
          ...prevTracks,
          {
            type: targetTrackType,
            isGlobalTrack: true,
            overlays: [{
              ...DEFAULT_OVERLAY,
              ...overlay,
              id
            } as Overlay]
          }
        ]
      }

      const targetTrack = cloneDeep(prevTracks[targetTrackIndex])
      const [, lastOverlayEnd] = getOverlayTimeRange(findLastOverlay(targetTrack.overlays))

      targetTrack.overlays.push({
        ...DEFAULT_OVERLAY,
        ...overlay,
        id,
        from: lastOverlayEnd,
      } as Overlay)

      return [
        ...prevTracks.slice(0, targetTrackIndex),
        targetTrack,
        ...prevTracks.slice(targetTrackIndex + 1)
      ]
    })
  }

  const moveTrack = (fromIndex: number, toIndex: number) => {
    updateTracks(prevTracks => {
      if (fromIndex < 0 || fromIndex >= prevTracks.length || toIndex < 0 || toIndex >= prevTracks.length) {
        return prevTracks
      }

      const newTracks = cloneDeep(prevTracks)
      // 保存要移动的 track
      const trackToMove = newTracks[fromIndex]

      // 从原位置删除
      newTracks.splice(fromIndex, 1)

      // 插入到目标位置
      newTracks.splice(toIndex, 0, trackToMove)

      return newTracks
    })
  }

  const generateDefaultCaptionOverlay = useCallback(
    (opts?: Partial<TextOverlay>) => {
      const {
        content = '默认文字',
        id = generateNewOverlayId(tracks),
        styles,
        ...restDefaultValues
      } = opts || {}

      const { playerWidth, playerHeight } = getPlayerDimensions()

      return {
        ...DEFAULT_OVERLAY,
        ...restDefaultValues,
        id,
        type: OverlayType.TEXT,
        content,
        src: TEXT_DEFAULT_CLOUD_FONT_SRC,
        width: playerWidth * 0.8,
        height: 100,
        left: playerWidth * 0.1,
        top: playerHeight * 0.8,
        styles: {
          ...styles,
          ...DEFAULT_TEXT_OVERLAY_STYLES,
          textAlign: 'center',
        },
      } as TextOverlay
    },
    [tracks]
  )

  return {
    addOverlayToGlobalTrack,
    appendOverlayToTrack,
    splitOverlay,
    changeOverlayPlaySpeed,
    moveOverlayToStoryboardIndexAt,
    moveTrack,
    addOverlayToTrack,
    generateDefaultCaptionOverlay,
  }
}
