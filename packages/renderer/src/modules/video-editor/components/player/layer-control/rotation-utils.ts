/**
 * 旋转坐标变换工具函数
 * 用于处理带有旋转角度的图层缩放操作
 */

/**
 * 将角度转换为弧度
 */
export function degToRad(degrees: number): number {
  return degrees * (Math.PI / 180)
}

/**
 * 将弧度转换为角度
 */
export function radToDeg(radians: number): number {
  return radians * (180 / Math.PI)
}

/**
 * 点的坐标结构
 */
export interface Point {
  x: number
  y: number
}

/**
 * 矩形的结构
 */
export interface Rect {
  left: number
  top: number
  width: number
  height: number
  rotation: number
}

/**
 * 围绕指定中心点旋转一个点
 * @param point 要旋转的点
 * @param center 旋转中心点
 * @param angle 旋转角度（度）
 * @returns 旋转后的点
 */
export function rotatePoint(point: Point, center: Point, angle: number): Point {
  const rad = degToRad(angle)
  const cos = Math.cos(rad)
  const sin = Math.sin(rad)
  
  const dx = point.x - center.x
  const dy = point.y - center.y
  
  return {
    x: center.x + dx * cos - dy * sin,
    y: center.y + dx * sin + dy * cos
  }
}

/**
 * 围绕指定中心点反向旋转一个点
 * @param point 要反向旋转的点
 * @param center 旋转中心点
 * @param angle 旋转角度（度）
 * @returns 反向旋转后的点
 */
export function rotatePointReverse(point: Point, center: Point, angle: number): Point {
  return rotatePoint(point, center, -angle)
}

/**
 * 获取矩形的中心点
 */
export function getRectCenter(rect: Rect): Point {
  return {
    x: rect.left + rect.width / 2,
    y: rect.top + rect.height / 2
  }
}

/**
 * 获取矩形的四个角点（考虑旋转）
 */
export function getRectCorners(rect: Rect): {
  topLeft: Point
  topRight: Point
  bottomLeft: Point
  bottomRight: Point
} {
  const center = getRectCenter(rect)
  
  // 未旋转时的四个角点
  const corners = {
    topLeft: { x: rect.left, y: rect.top },
    topRight: { x: rect.left + rect.width, y: rect.top },
    bottomLeft: { x: rect.left, y: rect.top + rect.height },
    bottomRight: { x: rect.left + rect.width, y: rect.top + rect.height }
  }
  
  // 如果有旋转，则旋转所有角点
  if (rect.rotation !== 0) {
    return {
      topLeft: rotatePoint(corners.topLeft, center, rect.rotation),
      topRight: rotatePoint(corners.topRight, center, rect.rotation),
      bottomLeft: rotatePoint(corners.bottomLeft, center, rect.rotation),
      bottomRight: rotatePoint(corners.bottomRight, center, rect.rotation)
    }
  }
  
  return corners
}

/**
 * 根据缩放手柄类型获取对应的固定点（对角线交点）
 */
export function getFixedPointForScaleHandle(
  rect: Rect,
  handleType: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
): Point {
  const corners = getRectCorners(rect)
  
  switch (handleType) {
    case 'top-left':
      return corners.bottomRight
    case 'top-right':
      return corners.bottomLeft
    case 'bottom-left':
      return corners.topRight
    case 'bottom-right':
      return corners.topLeft
    default:
      throw new Error(`Unknown handle type: ${handleType}`)
  }
}

/**
 * 根据缩放手柄类型获取当前拖拽点
 */
export function getDragPointForScaleHandle(
  rect: Rect,
  handleType: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
): Point {
  const corners = getRectCorners(rect)
  
  switch (handleType) {
    case 'top-left':
      return corners.topLeft
    case 'top-right':
      return corners.topRight
    case 'bottom-left':
      return corners.bottomLeft
    case 'bottom-right':
      return corners.bottomRight
    default:
      throw new Error(`Unknown handle type: ${handleType}`)
  }
}

/**
 * 计算两点之间的距离
 */
export function getDistance(point1: Point, point2: Point): number {
  const dx = point2.x - point1.x
  const dy = point2.y - point1.y
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * 根据固定点、新的拖拽点位置和缩放因子计算新的矩形
 */
export function calculateScaledRect(
  originalRect: Rect,
  fixedPoint: Point,
  newDragPoint: Point,
  handleType: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
): Rect {
  const originalCenter = getRectCenter(originalRect)
  const originalDragPoint = getDragPointForScaleHandle(originalRect, handleType)
  
  // 计算缩放因子
  const originalDistance = getDistance(fixedPoint, originalDragPoint)
  const newDistance = getDistance(fixedPoint, newDragPoint)
  const scaleFactor = originalDistance > 0 ? newDistance / originalDistance : 1
  
  // 确保最小缩放因子
  const minScaleFactor = Math.max(1 / originalRect.width, 1 / originalRect.height)
  const finalScaleFactor = Math.max(scaleFactor, minScaleFactor)
  
  // 计算新的尺寸
  const newWidth = originalRect.width * finalScaleFactor
  const newHeight = originalRect.height * finalScaleFactor
  
  // 计算新的中心点
  // 新中心点 = 固定点 + (原中心点 - 固定点) * 缩放因子
  const originalCenterToFixed = {
    x: originalCenter.x - fixedPoint.x,
    y: originalCenter.y - fixedPoint.y
  }
  
  const newCenter = {
    x: fixedPoint.x + originalCenterToFixed.x * finalScaleFactor,
    y: fixedPoint.y + originalCenterToFixed.y * finalScaleFactor
  }
  
  // 计算新的左上角位置
  const newLeft = newCenter.x - newWidth / 2
  const newTop = newCenter.y - newHeight / 2
  
  return {
    left: newLeft,
    top: newTop,
    width: newWidth,
    height: newHeight,
    rotation: originalRect.rotation // 保持旋转角度不变
  }
}

/**
 * 检查矩形是否在边界内（考虑旋转）
 */
export function isRectWithinBounds(rect: Rect, bounds: { width: number; height: number }): boolean {
  const corners = getRectCorners(rect)
  const allCorners = [corners.topLeft, corners.topRight, corners.bottomLeft, corners.bottomRight]
  
  return allCorners.every(corner => 
    corner.x >= 0 && corner.x <= bounds.width &&
    corner.y >= 0 && corner.y <= bounds.height
  )
}

/**
 * 获取旋转矩形的边界框（AABB - Axis Aligned Bounding Box）
 */
export function getRotatedRectBounds(rect: Rect): { left: number; top: number; right: number; bottom: number } {
  const corners = getRectCorners(rect)
  const allCorners = [corners.topLeft, corners.topRight, corners.bottomLeft, corners.bottomRight]
  
  const xs = allCorners.map(corner => corner.x)
  const ys = allCorners.map(corner => corner.y)
  
  return {
    left: Math.min(...xs),
    top: Math.min(...ys),
    right: Math.max(...xs),
    bottom: Math.max(...ys)
  }
}
