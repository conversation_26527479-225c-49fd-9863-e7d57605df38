import React, { useCallback, useEffect, useMemo, useRef } from 'react'
import { useCurrentScale } from 'remotion'
import { clamp, isEqual, omit } from 'lodash'
import { useQuery } from '@tanstack/react-query'
import { DndContext, DragMoveEvent, PointerSensor, useDraggable, useSensor, useSensors } from '@dnd-kit/core'

import { Overlay, OverlayType, RenderableOverlay } from '@clipnest/remotion-shared/types'
import { cacheManager } from '@/libs/cache/cache-manager'
import { useCachedOverlaysContext, useEditorContext } from '@/modules/video-editor/contexts'

import { ScaleHandle } from './scale-handle'
import { RotateHandle } from './rotate-handle'
import { ResizeHandle } from './resize-handle'
import { LAYER_CONTROL_DRAG_ACTIONS } from './constants'
import { getOverlayTimeRange } from '@/modules/video-editor/utils/overlay-helper'
import { calculateTextRenderInfo } from '@clipnest/overlay-renderer'
import {
  calculateMaxScaleFactor,
  calculateScaledRect,
  calculateScaledRectWithFactor,
  getDragPointForScaleHandle,
  getFixedPointForScaleHandle,
  Point,
  Rect
} from './rotation-utils'

type LayerControlProps = {
  overlay: Overlay
}

function useStateToRef<T>(state: T) {
  const ref = useRef<T>(state)

  useEffect(() => {
    ref.current = state
  }, [state])

  return ref
}

export type SelectionOutlineProps = {
  /**
   * The overlay object containing position, size, and other properties
   */
  overlay: RenderableOverlay
}

/**
 * SelectionOutline is a component that renders a draggable, resizable outline around selected overlays.
 * It provides visual feedback and interaction handles for manipulating overlay elements.
 *
 * @component
 */
const SelectionOutline: React.FC<SelectionOutlineProps> = ({ overlay }) => {
  const scale = useCurrentScale()
  const scaledBorder = Math.ceil(1 / scale)

  const {
    selectedOverlay,
    videoPlayer: { currentFrame },
    setSelectedOverlay
  } = useEditorContext()

  const ref = useRef<HTMLDivElement | null>(null)

  const { setNodeRef, listeners, attributes, isDragging } = useDraggable({
    id: `later-outline-${overlay.id}`,
    data: {
      action: LAYER_CONTROL_DRAG_ACTIONS.move,
      overlay
    }
  })

  const [hovered, setHovered] = React.useState(false)

  const enableToBeSelected = useMemo(() => {
    if (currentFrame === undefined) return false
    const [from, end] = getOverlayTimeRange(overlay)
    return from <= currentFrame && currentFrame < end
  }, [currentFrame, overlay])

  const onMouseEnter = useCallback(() => {
    if (enableToBeSelected) {
      setHovered(true)
    }
  }, [overlay, enableToBeSelected])

  const onMouseLeave = useCallback(() => {
    setHovered(false)
  }, [])

  const isSelected = useMemo(
    () => overlay.id === selectedOverlay?.id,
    [overlay.id, selectedOverlay]
  )

  const style: React.CSSProperties = useMemo(
    () => ({
      width: Number.isFinite(overlay.width) ? overlay.width : 0,
      height: Number.isFinite(overlay.height) ? overlay.height : 0,
      left: overlay.left,
      top: overlay.top,
      position: 'absolute',
      outline: (hovered && !isDragging) || isSelected
        ? `${scaledBorder}px solid #3B8BF2`
        : undefined,
      transform: `rotate(${overlay.rotation || 0}deg)`,
      transformOrigin: 'center center',
      userSelect: 'none',
      touchAction: 'none',
      zIndex: overlay.zIndex,
      pointerEvents: enableToBeSelected ? 'all' : 'none',
      cursor: 'pointer'
    }),
    [overlay, isDragging, hovered, isSelected, scaledBorder, enableToBeSelected]
  )

  if (overlay.type === OverlayType.SOUND) {
    return null
  }

  return (
    <>
      <div
        ref={el => {
          ref.current = el
          setNodeRef(el)
        }}
        {...listeners}
        {...attributes}
        onClick={() => setSelectedOverlay(overlay)}
        onPointerEnter={onMouseEnter}
        onPointerLeave={onMouseLeave}
        style={style}
      >
        {isSelected && (
          <>
            <RotateHandle
              overlay={overlay}
              outlineRef={ref}
            />

            {overlay.type !== OverlayType.TEXT ? (
              <>
                <ScaleHandle
                  overlay={overlay}
                  type="top-left"
                />
                <ScaleHandle
                  overlay={overlay}
                  type="top-right"
                />
                <ScaleHandle
                  overlay={overlay}
                  type="bottom-left"
                />
                <ScaleHandle
                  overlay={overlay}
                  type="bottom-right"
                />
              </>
            ) : (
              <ResizeHandle
                overlay={overlay}
                type="center-right"
              />
            )}
          </>
        )}
      </div>
    </>
  )
}

export const LayerControl: React.FC<LayerControlProps> = ({ overlay }) => {
  if (overlay.type === OverlayType.STORYBOARD) return null

  const scale = useCurrentScale()

  const { requestUpdate } = useCachedOverlaysContext()
  const { setSelectedOverlay, getPlayerDimensions } = useEditorContext()
  const { playerWidth, playerHeight } = getPlayerDimensions()

  const localOverlayRef = useStateToRef(overlay)
  const initialOverlayRef = useRef<Overlay | null>(null)

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 1
      }
    })
  )

  const handleDragStart = useCallback(() => {
    setSelectedOverlay?.(overlay)
    requestUpdate(overlay.id, prev => {
      initialOverlayRef.current = prev
      return {
        ...prev,
        isDragging: true
      }
    })
  }, [overlay, requestUpdate])

  const { data: font = null } = useQuery({
    queryKey: ['LAYER_CONTROL_FONT', 'src' in overlay && overlay.src],
    queryFn: () => {
      if (!('src' in overlay)) return null

      return cacheManager.font.getFont(overlay.src)
    },
    enabled: overlay.type === OverlayType.TEXT && 'src' in overlay,
    refetchInterval: query => {
      if (query.state.data) return false
      return 1000
    }
  })

  const handleResize = useCallback(
    (initial: Overlay, deltaX: number) => {
      const newWidth = initial.width + deltaX

      const maybeTextRenderInfo = font && overlay.type === OverlayType.TEXT
        ? calculateTextRenderInfo(font, overlay, { width: newWidth })
        : null

      const constrainedWidth = clamp(
        Math.round(newWidth),
        maybeTextRenderInfo?.minWidth || 1,
        playerWidth
      )

      const constrainedLeft = clamp(Math.round(initial.left), 0, playerWidth - constrainedWidth)

      requestUpdate(overlay.id, {
        left: constrainedLeft,
        width: constrainedWidth,
        ...(maybeTextRenderInfo && { height: maybeTextRenderInfo.minHeight })
      })
    },
    [font, overlay]
  )

  const handleDragMove = useCallback(
    (event: DragMoveEvent) => {
      const initial = initialOverlayRef.current
      if (!initial) return

      const { action, type, outlineRef } = event.active.data.current || {}

      const { clientX: initialClientX, clientY: initialClientY } = event.activatorEvent as PointerEvent
      const deltaX = event.delta.x / scale
      const deltaY = event.delta.y / scale

      if (action === LAYER_CONTROL_DRAG_ACTIONS.move) {
        return requestUpdate(overlay.id, {
          left: initial.left + deltaX,
          top: initial.top + deltaY
        })
      }

      if (action === LAYER_CONTROL_DRAG_ACTIONS.scale) {
        // 创建初始矩形对象
        const initialRect: Rect = {
          left: initial.left,
          top: initial.top,
          width: initial.width,
          height: initial.height,
          rotation: initial.rotation || 0
        }

        // 获取固定点（对角线交点，作为缩放原点）
        const fixedPoint = getFixedPointForScaleHandle(initialRect, type)

        // 获取初始拖拽点位置
        const initialDragPoint = getDragPointForScaleHandle(initialRect, type)

        // 计算新的拖拽点位置
        const newDragPoint: Point = {
          x: initialDragPoint.x + deltaX,
          y: initialDragPoint.y + deltaY
        }

        // 首先计算理想的缩放矩形（不考虑边界限制）
        let scaledRect = calculateScaledRect(initialRect, fixedPoint, newDragPoint, type)

        // 边界检查和约束
        // 计算最大允许的缩放因子，确保缩放后的矩形不会超出边界
        const maxScaleFactor = calculateMaxScaleFactor(initialRect, fixedPoint, playerWidth, playerHeight)

        // 获取当前缩放因子
        const currentScaleFactor = scaledRect.width / initialRect.width

        // 如果当前缩放因子超过最大允许值，则限制缩放
        if (currentScaleFactor > maxScaleFactor) {
          // 重新计算受限制的缩放矩形
          scaledRect = calculateScaledRectWithFactor(initialRect, fixedPoint, maxScaleFactor)
        }

        // 最终约束确保值在有效范围内
        const constrainedWidth = clamp(Math.round(scaledRect.width), 1, playerWidth)
        const constrainedHeight = clamp(Math.round(scaledRect.height), 1, playerHeight)
        const constrainedLeft = Math.round(scaledRect.left)
        const constrainedTop = Math.round(scaledRect.top)

        return requestUpdate(overlay.id, {
          left: constrainedLeft,
          top: constrainedTop,
          width: constrainedWidth,
          height: constrainedHeight,
          rotation: scaledRect.rotation
        })
      }

      if (action === LAYER_CONTROL_DRAG_ACTIONS.resize) {
        return handleResize(initial, deltaX)
      }

      if (action === LAYER_CONTROL_DRAG_ACTIONS.rotate) {
        const rect = outlineRef.current.getBoundingClientRect()

        const centerX = rect.left + rect.width / 2
        const centerY = rect.top + rect.height / 2

        const getAngle = (x: number, y: number) => {
          const deltaX = x - centerX
          const deltaY = y - centerY
          const base = Math.atan2(deltaY, deltaX) * (180 / Math.PI) + 90
          return base <= 180 ? base : base - 360
        }

        const currentAngle = getAngle(
          initialClientX + event.delta.x,
          initialClientY + event.delta.y
        )

        return requestUpdate(overlay.id, {
          rotation: currentAngle
        })
      }
    },
    [scale, requestUpdate, handleResize]
  )

  const handleDragEnd = useCallback(() => {
    const initial = initialOverlayRef.current
    const current = localOverlayRef.current
    if (!isEqual(omit(current, 'isDragging'), omit(initial, 'isDragging'))) {
      requestUpdate?.(overlay.id, current, true)
    }
  }, [requestUpdate])

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
    >
      <SelectionOutline overlay={overlay as any} />
    </DndContext>
  )
}
